#!/usr/bin/env python3
"""
Main entry point for the Zombie IP S3 Ingest application.
"""

import sys
import logging
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from zombie_ip_ingest.app import main as app_main

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """Main entry point."""
    try:
        logger.info("Starting Zombie IP S3 Ingest application")
        app_main()
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Application failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
