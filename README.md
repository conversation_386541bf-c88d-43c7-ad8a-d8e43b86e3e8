# Zombie IP S3 Ingest

A Python project for processing and ingesting zombie IP data from S3.

## Setup

1. Create and activate virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

```bash
python main.py
```

## Development

### Running Tests
```bash
python -m pytest tests/
```

### Project Structure
```
├── src/                    # Source code
│   └── zombie_ip_ingest/   # Main package
├── tests/                  # Test files
├── requirements.txt        # Dependencies
├── setup.py               # Package setup
└── main.py                # Entry point
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request
