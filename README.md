# Zombie IP S3 Ingest

A Python project for processing and ingesting zombie IP data from S3 with advanced batch processing capabilities.

## Features

- **Batch Processing**: Process multiple S3 files in configurable batches
- **Parallel Processing**: Download and process files concurrently for improved performance
- **Flexible Data Formats**: Supports JSON, JSONL (JSON Lines), and plain text formats
- **Error Handling**: Robust error handling with retry logic and continue-on-error options
- **Progress Tracking**: Detailed logging and statistics for monitoring processing progress
- **Configurable**: Extensive configuration options via environment variables

## Setup

1. Create and activate virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure environment variables (copy from .env.example):
```bash
cp .env.example .env
# Edit .env with your AWS credentials and configuration
```

## Usage

### Basic Usage
```bash
python main.py
```

### Test the Processing Logic
```bash
python test_batch_processing.py
```

## Configuration

The application uses environment variables for configuration. Key settings include:

### AWS Configuration
- `AWS_ACCESS_KEY_ID`: Your AWS access key
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
- `AWS_REGION`: AWS region (default: us-east-1)

### S3 Configuration
- `S3_BUCKET_NAME`: Name of the S3 bucket containing IP data
- `S3_PREFIX`: Prefix/folder path within the bucket (optional)

### Processing Configuration
- `FILE_BATCH_SIZE`: Number of files to process in each batch (default: 10)
- `BATCH_SIZE`: Number of records to process in each batch (default: 1000)
- `MAX_WORKERS`: Number of parallel workers for file downloads (default: 4)
- `MAX_FILE_SIZE_MB`: Maximum file size to process in MB (default: 100)
- `ENABLE_PARALLEL_PROCESSING`: Enable parallel file processing (default: true)

### Error Handling
- `MAX_RETRIES`: Number of retry attempts for failed operations (default: 3)
- `RETRY_DELAY_SECONDS`: Delay between retry attempts (default: 5)
- `CONTINUE_ON_ERROR`: Continue processing other files if one fails (default: true)

## Data Format Support

The system automatically detects and processes multiple data formats:

### JSON Format
Single JSON object or array of objects:
```json
{"payload": {"identifier": "***********", "detection": {"risk": 95}}}
```

### JSONL Format (JSON Lines)
Multiple JSON objects, one per line:
```
{"payload": {"identifier": "***********", "detection": {"risk": 95}}}
{"payload": {"identifier": "********", "detection": {"risk": 80}}}
```

### Plain Text
IP addresses, one per line:
```
***********
********
***********
```

## Customizing Data Processing

To customize how processed data is handled, edit the `_handle_processed_data` method in `src/zombie_ip_ingest/batch_processor.py`:

```python
def _handle_processed_data(self, file_key: str, processed_records: List[Dict[str, Any]]):
    # Your custom logic here:
    # - Send to database
    # - Write to files
    # - Send to API
    # - Generate alerts
    pass
```

## Development

### Running Tests
```bash
python -m pytest tests/
```

### Test Batch Processing
```bash
python test_batch_processing.py
```

### Project Structure
```
├── src/                    # Source code
│   └── zombie_ip_ingest/   # Main package
│       ├── app.py          # Main application
│       ├── batch_processor.py  # Batch processing logic
│       ├── config.py       # Configuration management
│       ├── processor.py    # Data processing logic
│       └── s3_client.py    # S3 interaction
├── tests/                  # Test files
├── requirements.txt        # Dependencies
├── test_batch_processing.py # Batch processing tests
└── main.py                # Entry point
```

## Performance Considerations

- **File Batch Size**: Larger batches reduce S3 API calls but use more memory
- **Parallel Processing**: More workers speed up downloads but increase resource usage
- **File Size Limits**: Large files are automatically skipped to prevent memory issues
- **Error Handling**: Continue-on-error mode prevents single file failures from stopping the entire process

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request
