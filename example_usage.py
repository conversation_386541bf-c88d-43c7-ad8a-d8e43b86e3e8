#!/usr/bin/env python3
"""
Example usage of the Zombie IP S3 Ingest batch processing system.
"""

import sys
import os
import logging
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from zombie_ip_ingest.app import ZombieIP<PERSON>ngestApp
from zombie_ip_ingest.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def example_batch_processing():
    """Example of running batch processing with custom configuration."""
    logger.info("Example: Batch Processing with Custom Configuration")

    # You can override configuration programmatically
    os.environ.update({
        'FILE_BATCH_SIZE': '5',  # Process 5 files at a time
        'MAX_WORKERS': '2',      # Use 2 parallel workers
        'CONTINUE_ON_ERROR': 'true',  # Continue if individual files fail
        'MAX_RETRIES': '2',      # Retry failed downloads 2 times
        # Set dummy values for demo purposes
        'AWS_ACCESS_KEY_ID': 'demo_key',
        'AWS_SECRET_ACCESS_KEY': 'demo_secret',
        'S3_BUCKET_NAME': 'demo_bucket',
    })

    try:
        # Initialize the application
        app = ZombieIPIngestApp()

        # Display configuration
        config = app.config
        logger.info("Current Configuration:")
        logger.info(f"  File Batch Size: {config.file_batch_size}")
        logger.info(f"  Record Batch Size: {config.batch_size}")
        logger.info(f"  Max Workers: {config.max_workers}")
        logger.info(f"  Max File Size: {config.max_file_size_mb}MB")
        logger.info(f"  Parallel Processing: {config.enable_parallel_processing}")
        logger.info(f"  Continue on Error: {config.continue_on_error}")
        logger.info(f"  Max Retries: {config.max_retries}")

    except Exception as e:
        logger.info(f"Note: AWS initialization failed (expected for demo): {e}")

    # Note: This would actually try to connect to S3, so we'll just show the setup
    logger.info("To run actual batch processing, ensure your AWS credentials are configured:")
    logger.info("1. Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables")
    logger.info("2. Set S3_BUCKET_NAME to your bucket name")
    logger.info("3. Optionally set S3_PREFIX to filter files")
    logger.info("4. Then call: app.run()")

    # Example of processing a single file (if you have the file key)
    # result = app.run_single_file('path/to/your/file.json')
    # logger.info(f"Single file result: {result}")


def example_custom_data_handler():
    """Example of how to customize the data processing logic."""
    logger.info("Example: Custom Data Processing")
    
    logger.info("To customize data processing, edit the _handle_processed_data method in:")
    logger.info("src/zombie_ip_ingest/batch_processor.py")
    
    logger.info("\nExample customizations:")
    logger.info("1. Save to database:")
    logger.info("   for record in processed_records:")
    logger.info("       database.insert_ip_record(record)")
    
    logger.info("\n2. Filter high-risk IPs:")
    logger.info("   high_risk = [r for r in processed_records if r.get('risk_score', 0) > 80]")
    logger.info("   if high_risk:")
    logger.info("       alert_system.send_alert(high_risk)")
    
    logger.info("\n3. Save to JSON files:")
    logger.info("   output_file = f'output/{file_key.replace(\"/\", \"_\")}.json'")
    logger.info("   with open(output_file, 'w') as f:")
    logger.info("       json.dump(processed_records, f, indent=2)")
    
    logger.info("\n4. Send to API:")
    logger.info("   response = requests.post('https://api.example.com/ips', json=processed_records)")


def example_configuration_options():
    """Example of different configuration scenarios."""
    logger.info("Example: Configuration Scenarios")
    
    scenarios = [
        {
            "name": "High Throughput",
            "description": "For processing many small files quickly",
            "config": {
                "FILE_BATCH_SIZE": "20",
                "MAX_WORKERS": "8",
                "ENABLE_PARALLEL_PROCESSING": "true",
                "MAX_FILE_SIZE_MB": "50"
            }
        },
        {
            "name": "Large Files",
            "description": "For processing fewer, larger files",
            "config": {
                "FILE_BATCH_SIZE": "3",
                "MAX_WORKERS": "2",
                "ENABLE_PARALLEL_PROCESSING": "true",
                "MAX_FILE_SIZE_MB": "500"
            }
        },
        {
            "name": "Conservative",
            "description": "For reliable processing with error handling",
            "config": {
                "FILE_BATCH_SIZE": "5",
                "MAX_WORKERS": "2",
                "MAX_RETRIES": "5",
                "RETRY_DELAY_SECONDS": "10",
                "CONTINUE_ON_ERROR": "true"
            }
        },
        {
            "name": "Sequential",
            "description": "For debugging or limited resources",
            "config": {
                "FILE_BATCH_SIZE": "1",
                "MAX_WORKERS": "1",
                "ENABLE_PARALLEL_PROCESSING": "false",
                "CONTINUE_ON_ERROR": "false"
            }
        }
    ]
    
    for scenario in scenarios:
        logger.info(f"\n{scenario['name']} Scenario:")
        logger.info(f"  Description: {scenario['description']}")
        logger.info("  Configuration:")
        for key, value in scenario['config'].items():
            logger.info(f"    {key}={value}")


def main():
    """Run all examples."""
    logger.info("Zombie IP S3 Ingest - Usage Examples")
    logger.info("=" * 50)
    
    examples = [
        ("Batch Processing Setup", example_batch_processing),
        ("Custom Data Handler", example_custom_data_handler),
        ("Configuration Options", example_configuration_options),
    ]
    
    for example_name, example_func in examples:
        logger.info(f"\n{'='*60}")
        logger.info(f"{example_name}")
        logger.info(f"{'='*60}")
        example_func()
    
    logger.info(f"\n{'='*60}")
    logger.info("For more information, see README.md")
    logger.info(f"{'='*60}")


if __name__ == "__main__":
    main()
