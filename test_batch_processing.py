#!/usr/bin/env python3
"""
Test script for batch processing functionality.
"""

import sys
import json
import logging
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from zombie_ip_ingest.processor import IPProcessor
from zombie_ip_ingest.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_sample_data():
    """Test processing the sample data."""
    logger.info("Testing sample data processing")
    
    # Read sample data
    with open('sample', 'r') as f:
        sample_data = f.read().strip()
    
    # Initialize processor
    config = Config()
    processor = IPProcessor(config)
    
    # Process the sample data
    try:
        processed_data = processor.process(sample_data.encode('utf-8'))
        
        logger.info(f"Successfully processed sample data")
        logger.info(f"Number of records extracted: {len(processed_data)}")
        
        if processed_data:
            # Pretty print the first record
            first_record = processed_data[0]
            logger.info("First processed record:")
            print(json.dumps(first_record, indent=2, default=str))
            
            # Show key extracted fields
            logger.info("Key extracted fields:")
            logger.info(f"  IP Address: {first_record.get('ip_address')}")
            logger.info(f"  Risk Score: {first_record.get('risk_score')}")
            logger.info(f"  Detection Categories: {first_record.get('detection_category')}")
            logger.info(f"  Country Code: {first_record.get('country_code')}")
            logger.info(f"  Detection Methods: {first_record.get('detection_methods')}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error processing sample data: {e}")
        return False


def test_jsonl_data():
    """Test processing multiple JSON objects (JSONL format)."""
    logger.info("Testing JSONL data processing")
    
    # Create test JSONL data with multiple records
    test_data = '''{"payload":{"identifier":"*************","first_seen":"2025-01-01T10:00:00Z","detection":{"risk":95,"category":["Malware"]},"meta":{"country_code":"US","port":443}},"timestamp":"2025-01-01T10:00:00Z"}
{"payload":{"identifier":"*********","first_seen":"2025-01-01T11:00:00Z","detection":{"risk":75,"category":["Phishing"]},"meta":{"country_code":"CA","port":80}},"timestamp":"2025-01-01T11:00:00Z"}
{"payload":{"identifier":"***********","first_seen":"2025-01-01T12:00:00Z","detection":{"risk":60,"category":["Spam"]},"meta":{"country_code":"AU","port":25}},"timestamp":"2025-01-01T12:00:00Z"}'''
    
    # Initialize processor
    config = Config()
    processor = IPProcessor(config)
    
    try:
        processed_data = processor.process(test_data.encode('utf-8'))
        
        logger.info(f"Successfully processed JSONL test data")
        logger.info(f"Number of records extracted: {len(processed_data)}")
        
        for i, record in enumerate(processed_data):
            logger.info(f"Record {i+1}: IP={record.get('ip_address')}, "
                       f"Risk={record.get('risk_score')}, "
                       f"Country={record.get('country_code')}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error processing JSONL test data: {e}")
        return False


def main():
    """Run all tests."""
    logger.info("Starting batch processing tests")
    
    tests = [
        ("Sample Data Processing", test_sample_data),
        ("JSONL Data Processing", test_jsonl_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "PASSED" if result else "FAILED"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            logger.error(f"{test_name}: FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("All tests passed! The batch processing system is ready.")
    else:
        logger.warning("Some tests failed. Please check the logs above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
