#!/usr/bin/env python3
"""
Test script for batch processing functionality.
"""

import sys
import json
import logging
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from zombie_ip_ingest.processor import IPProcessor
from zombie_ip_ingest.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_sample_data():
    """Test processing the sample data."""
    logger.info("Testing sample data processing")
    
    # Read sample data
    with open('sample', 'r') as f:
        sample_data = f.read().strip()
    
    # Initialize processor
    config = Config()
    processor = IPProcessor(config)
    
    # Process the sample data
    try:
        processed_data = processor.process(sample_data.encode('utf-8'))

        logger.info(f"Successfully processed sample data")

        if processed_data:
            logger.info("Number of records extracted: 1")
            # Pretty print the record
            logger.info("Processed record:")
            print(json.dumps(processed_data, indent=2, default=str))

            # Show key extracted fields
            logger.info("Key extracted fields:")
            logger.info(f"  IP Address: {processed_data.get('ip_address')}")
            logger.info(f"  Risk Score: {processed_data.get('risk_score')}")
            logger.info(f"  Detection Categories: {processed_data.get('detection_category')}")
            logger.info(f"  Country Code: {processed_data.get('country_code')}")
            logger.info(f"  Detection Methods: {processed_data.get('detection_methods')}")
        else:
            logger.warning("No data extracted from sample")
            return False

        return True
        
    except Exception as e:
        logger.error(f"Error processing sample data: {e}")
        return False


def test_text_data():
    """Test processing plain text IP data."""
    logger.info("Testing plain text IP processing")

    # Create test text data with single IP
    test_data = "*************"

    # Initialize processor
    config = Config()
    processor = IPProcessor(config)

    try:
        processed_data = processor.process(test_data.encode('utf-8'))

        logger.info(f"Successfully processed text test data")

        if processed_data:
            logger.info("Number of records extracted: 1")
            logger.info(f"IP Address: {processed_data.get('ip_address')}")
            logger.info(f"IP Version: {processed_data.get('ip_version')}")
            logger.info(f"Is Private: {processed_data.get('is_private')}")
        else:
            logger.warning("No data extracted from text")
            return False

        return True

    except Exception as e:
        logger.error(f"Error processing text test data: {e}")
        return False


def main():
    """Run all tests."""
    logger.info("Starting batch processing tests")
    
    tests = [
        ("Sample Data Processing", test_sample_data),
        ("Text Data Processing", test_text_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "PASSED" if result else "FAILED"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            logger.error(f"{test_name}: FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("All tests passed! The batch processing system is ready.")
    else:
        logger.warning("Some tests failed. Please check the logs above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
