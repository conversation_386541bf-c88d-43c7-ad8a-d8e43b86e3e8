"""
Tests for the IP processor module.
"""

import pytest
import json
from unittest.mock import Mock
import sys
from pathlib import Path

# Add src to path for testing
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from zombie_ip_ingest.processor import IPProcessor
from zombie_ip_ingest.config import Config


class TestIPProcessor:
    """Test cases for IPProcessor class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = Mock(spec=Config)
        self.processor = IPProcessor(self.config)
    
    def test_validate_and_process_ip_valid_ipv4(self):
        """Test processing valid IPv4 address."""
        result = self.processor._validate_and_process_ip("***********")
        
        assert result is not None
        assert result['ip_address'] == "***********"
        assert result['ip_version'] == 4
        assert result['is_private'] is True
        assert 'processed_timestamp' in result
    
    def test_validate_and_process_ip_valid_ipv6(self):
        """Test processing valid IPv6 address."""
        result = self.processor._validate_and_process_ip("2001:db8::1")
        
        assert result is not None
        assert result['ip_address'] == "2001:db8::1"
        assert result['ip_version'] == 6
        assert 'processed_timestamp' in result
    
    def test_validate_and_process_ip_invalid(self):
        """Test processing invalid IP address."""
        result = self.processor._validate_and_process_ip("not.an.ip.address")
        assert result is None
    
    def test_process_text_data(self):
        """Test processing text data with IP addresses."""
        text_data = "***********\n********\ninvalid.ip\n*******"
        data = text_data.encode('utf-8')
        
        results = self.processor.process(data)
        
        assert len(results) == 3  # Should skip invalid IP
        assert results[0]['ip_address'] == "***********"
        assert results[1]['ip_address'] == "********"
        assert results[2]['ip_address'] == "*******"
    
    def test_process_json_data(self):
        """Test processing JSON data with IP addresses."""
        json_data = [
            {"ip": "***********", "port": 80},
            {"ip_address": "********", "service": "ssh"},
            {"host": "*******", "type": "dns"}
        ]
        data = json.dumps(json_data).encode('utf-8')
        
        results = self.processor.process(data)
        
        assert len(results) == 3
        assert results[0]['ip_address'] == "***********"
        assert results[0]['port'] == 80
        assert results[1]['ip_address'] == "********"
        assert results[1]['service'] == "ssh"
        assert results[2]['ip_address'] == "*******"
        assert results[2]['type'] == "dns"
