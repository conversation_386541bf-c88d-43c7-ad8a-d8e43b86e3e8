"""
Tests for the configuration module.
"""

import pytest
import os
from unittest.mock import patch
import sys
from pathlib import Path

# Add src to path for testing
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from zombie_ip_ingest.config import Config


class TestConfig:
    """Test cases for Config class."""
    
    @patch.dict(os.environ, {
        'AWS_ACCESS_KEY_ID': 'test_key',
        'AWS_SECRET_ACCESS_KEY': 'test_secret',
        'AWS_REGION': 'us-west-2',
        'S3_BUCKET_NAME': 'test-bucket',
        'S3_PREFIX': 'test-prefix/',
        'BATCH_SIZE': '500',
        'MAX_WORKERS': '8'
    })
    def test_config_initialization_with_env_vars(self):
        """Test config initialization with environment variables."""
        config = Config()
        
        assert config.aws_access_key_id == 'test_key'
        assert config.aws_secret_access_key == 'test_secret'
        assert config.aws_region == 'us-west-2'
        assert config.s3_bucket_name == 'test-bucket'
        assert config.s3_prefix == 'test-prefix/'
        assert config.batch_size == 500
        assert config.max_workers == 8
    
    @patch.dict(os.environ, {}, clear=True)
    def test_config_defaults(self):
        """Test config defaults when no environment variables are set."""
        config = Config()
        
        assert config.aws_region == 'us-east-1'
        assert config.s3_prefix == ''
        assert config.batch_size == 1000
        assert config.max_workers == 4
        assert config.output_format == 'json'
        assert config.output_path == './output'
    
    @patch.dict(os.environ, {
        'AWS_ACCESS_KEY_ID': 'test_key',
        'AWS_SECRET_ACCESS_KEY': 'test_secret',
        'S3_BUCKET_NAME': 'test-bucket'
    })
    def test_config_validation_success(self):
        """Test successful config validation."""
        config = Config()
        assert config.validate() is True
    
    @patch.dict(os.environ, {
        'AWS_ACCESS_KEY_ID': 'test_key'
        # Missing AWS_SECRET_ACCESS_KEY and S3_BUCKET_NAME
    })
    def test_config_validation_failure(self):
        """Test config validation failure with missing required fields."""
        config = Config()
        
        with pytest.raises(ValueError) as exc_info:
            config.validate()
        
        assert "Missing required configuration" in str(exc_info.value)
        assert "AWS_SECRET_ACCESS_KEY" in str(exc_info.value)
        assert "S3_BUCKET_NAME" in str(exc_info.value)
    
    @patch.dict(os.environ, {
        'AWS_ACCESS_KEY_ID': 'test_key',
        'AWS_SECRET_ACCESS_KEY': 'test_secret',
        'AWS_REGION': 'us-west-2',
        'S3_BUCKET_NAME': 'test-bucket'
    })
    def test_config_repr(self):
        """Test config string representation."""
        config = Config()
        repr_str = repr(config)
        
        assert 'aws_region' in repr_str
        assert 's3_bucket_name' in repr_str
        assert 'us-west-2' in repr_str
        assert 'test-bucket' in repr_str
        # Should not contain sensitive data
        assert 'test_key' not in repr_str
        assert 'test_secret' not in repr_str
