"""
S3 client module for interacting with AWS S3.
"""

import logging
from typing import List, Dict, Any, Iterator, <PERSON><PERSON>
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import time
from .config import Config

logger = logging.getLogger(__name__)


class S3Client:
    """Client for interacting with AWS S3."""
    
    def __init__(self, config: Config):
        """Initialize S3 client with configuration."""
        self.config = config
        self.config.validate()
        
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=config.aws_access_key_id,
                aws_secret_access_key=config.aws_secret_access_key,
                region_name=config.aws_region
            )
            logger.info(f"S3 client initialized for region: {config.aws_region}")
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {e}")
            raise
    
    def list_objects(self) -> List[str]:
        """List objects in the configured S3 bucket."""
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.config.s3_bucket_name,
                Prefix=self.config.s3_prefix
            )
            
            objects = []
            if 'Contents' in response:
                objects = [obj['Key'] for obj in response['Contents']]
                logger.info(f"Found {len(objects)} objects in bucket {self.config.s3_bucket_name}")
            else:
                logger.warning(f"No objects found in bucket {self.config.s3_bucket_name}")
            
            return objects
            
        except ClientError as e:
            logger.error(f"Error listing S3 objects: {e}")
            raise
    
    def download_object(self, key: str) -> bytes:
        """Download an object from S3."""
        try:
            response = self.s3_client.get_object(
                Bucket=self.config.s3_bucket_name,
                Key=key
            )
            data = response['Body'].read()
            logger.info(f"Downloaded object {key}, size: {len(data)} bytes")
            return data
            
        except ClientError as e:
            logger.error(f"Error downloading S3 object {key}: {e}")
            raise
    
    def upload_object(self, key: str, data: bytes) -> bool:
        """Upload an object to S3."""
        try:
            self.s3_client.put_object(
                Bucket=self.config.s3_bucket_name,
                Key=key,
                Body=data
            )
            logger.info(f"Uploaded object {key}, size: {len(data)} bytes")
            return True

        except ClientError as e:
            logger.error(f"Error uploading S3 object {key}: {e}")
            raise

    def list_objects_paginated(self, max_keys: int = 1000) -> Iterator[List[Dict[str, Any]]]:
        """List objects in the S3 bucket with pagination support."""
        try:
            paginator = self.s3_client.get_paginator('list_objects_v2')
            page_iterator = paginator.paginate(
                Bucket=self.config.s3_bucket_name,
                Prefix=self.config.s3_prefix,
                PaginationConfig={'PageSize': max_keys}
            )

            for page in page_iterator:
                if 'Contents' in page:
                    objects = []
                    for obj in page['Contents']:
                        size_mb = obj['Size'] / (1024 * 1024)
                        objects.append({
                            'Key': obj['Key'],
                            'Size': obj['Size'],
                            'LastModified': obj['LastModified'],
                            'SizeMB': round(size_mb, 2)
                        })

                    if objects:
                        logger.info(f"Found {len(objects)} objects in page")
                        yield objects
                else:
                    logger.info("No objects found in current page")

        except ClientError as e:
            logger.error(f"Error listing S3 objects with pagination: {e}")
            raise

    def get_object_batches(self, batch_size: int) -> Iterator[List[Dict[str, Any]]]:
        """Get objects in batches of specified size."""
        current_batch = []

        for page in self.list_objects_paginated():
            for obj in page:
                current_batch.append(obj)

                if len(current_batch) >= batch_size:
                    yield current_batch
                    current_batch = []

        # Yield remaining objects if any
        if current_batch:
            yield current_batch

    def download_objects_batch(self, object_keys: List[str]) -> List[Tuple[str, bytes, bool]]:
        """Download multiple objects in parallel. Returns list of (key, data, success)."""
        results = []

        if self.config.enable_parallel_processing and len(object_keys) > 1:
            # Use parallel processing for multiple files
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                future_to_key = {
                    executor.submit(self._download_single_object_with_retry, key): key
                    for key in object_keys
                }

                for future in as_completed(future_to_key):
                    key = future_to_key[future]
                    try:
                        data = future.result()
                        results.append((key, data, True))
                        logger.info(f"Successfully downloaded {key}")
                    except Exception as e:
                        logger.error(f"Failed to download {key}: {e}")
                        results.append((key, b'', False))
        else:
            # Sequential processing
            for key in object_keys:
                try:
                    data = self._download_single_object_with_retry(key)
                    results.append((key, data, True))
                    logger.info(f"Successfully downloaded {key}")
                except Exception as e:
                    logger.error(f"Failed to download {key}: {e}")
                    results.append((key, b'', False))

        return results

    def _download_single_object_with_retry(self, key: str) -> bytes:
        """Download a single object with retry logic."""
        for attempt in range(self.config.max_retries + 1):
            try:
                response = self.s3_client.get_object(
                    Bucket=self.config.s3_bucket_name,
                    Key=key
                )
                data = response['Body'].read()
                logger.debug(f"Downloaded object {key}, size: {len(data)} bytes (attempt {attempt + 1})")
                return data

            except ClientError as e:
                if attempt < self.config.max_retries:
                    logger.warning(f"Attempt {attempt + 1} failed for {key}: {e}. Retrying in {self.config.retry_delay_seconds}s...")
                    time.sleep(self.config.retry_delay_seconds)
                else:
                    logger.error(f"All {self.config.max_retries + 1} attempts failed for {key}: {e}")
                    raise
