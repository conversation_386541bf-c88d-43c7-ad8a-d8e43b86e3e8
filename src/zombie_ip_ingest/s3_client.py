"""
S3 client module for interacting with AWS S3.
"""

import logging
from typing import List, Dict, Any
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from .config import Config

logger = logging.getLogger(__name__)


class S3Client:
    """Client for interacting with AWS S3."""
    
    def __init__(self, config: Config):
        """Initialize S3 client with configuration."""
        self.config = config
        self.config.validate()
        
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=config.aws_access_key_id,
                aws_secret_access_key=config.aws_secret_access_key,
                region_name=config.aws_region
            )
            logger.info(f"S3 client initialized for region: {config.aws_region}")
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {e}")
            raise
    
    def list_objects(self) -> List[str]:
        """List objects in the configured S3 bucket."""
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.config.s3_bucket_name,
                Prefix=self.config.s3_prefix
            )
            
            objects = []
            if 'Contents' in response:
                objects = [obj['Key'] for obj in response['Contents']]
                logger.info(f"Found {len(objects)} objects in bucket {self.config.s3_bucket_name}")
            else:
                logger.warning(f"No objects found in bucket {self.config.s3_bucket_name}")
            
            return objects
            
        except ClientError as e:
            logger.error(f"Error listing S3 objects: {e}")
            raise
    
    def download_object(self, key: str) -> bytes:
        """Download an object from S3."""
        try:
            response = self.s3_client.get_object(
                Bucket=self.config.s3_bucket_name,
                Key=key
            )
            data = response['Body'].read()
            logger.info(f"Downloaded object {key}, size: {len(data)} bytes")
            return data
            
        except ClientError as e:
            logger.error(f"Error downloading S3 object {key}: {e}")
            raise
    
    def upload_object(self, key: str, data: bytes) -> bool:
        """Upload an object to S3."""
        try:
            self.s3_client.put_object(
                Bucket=self.config.s3_bucket_name,
                Key=key,
                Body=data
            )
            logger.info(f"Uploaded object {key}, size: {len(data)} bytes")
            return True
            
        except ClientError as e:
            logger.error(f"Error uploading S3 object {key}: {e}")
            raise
