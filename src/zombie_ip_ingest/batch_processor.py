"""
Batch processor module for handling multiple files and batch processing.
"""

import logging
from typing import List, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from datetime import datetime

from .config import Config
from .s3_client import S3Client
from .processor import IPProcessor

logger = logging.getLogger(__name__)


class BatchProcessor:
    """Handles batch processing of multiple S3 files."""
    
    def __init__(self, config: Config, s3_client: S3Client, processor: IPProcessor):
        """Initialize batch processor with dependencies."""
        self.config = config
        self.s3_client = s3_client
        self.processor = processor
        self.stats = {
            'total_files_processed': 0,
            'total_files_failed': 0,
            'total_records_processed': 0,
            'total_batches_processed': 0,
            'start_time': None,
            'end_time': None
        }
    
    def process_all_files(self) -> Dict[str, Any]:
        """Process all files in S3 bucket in batches."""
        logger.info("Starting batch processing of all S3 files")
        self.stats['start_time'] = datetime.utcnow()
        
        try:
            batch_count = 0
            for file_batch in self.s3_client.get_object_batches(self.config.file_batch_size):
                batch_count += 1
                logger.info(f"Processing file batch {batch_count} with {len(file_batch)} files")
                
                batch_results = self._process_file_batch(file_batch, batch_count)
                self._update_stats(batch_results)
                
                # Log progress
                logger.info(f"Batch {batch_count} completed. "
                          f"Files processed: {self.stats['total_files_processed']}, "
                          f"Records processed: {self.stats['total_records_processed']}")
            
            self.stats['end_time'] = datetime.utcnow()
            self.stats['total_batches_processed'] = batch_count
            
            # Log final statistics
            self._log_final_stats()
            
            return self.stats
            
        except Exception as e:
            logger.error(f"Error during batch processing: {e}")
            self.stats['end_time'] = datetime.utcnow()
            raise
    
    def _process_file_batch(self, file_batch: List[Dict[str, Any]], batch_num: int) -> List[Dict[str, Any]]:
        """Process a batch of files."""
        file_keys = [obj['Key'] for obj in file_batch]
        
        logger.info(f"Downloading {len(file_keys)} files for batch {batch_num}")
        download_results = self.s3_client.download_objects_batch(file_keys)
        
        batch_results = []
        
        for key, data, success in download_results:
            file_result = {
                'file_key': key,
                'success': success,
                'records_processed': 0,
                'error': None,
                'processing_time_seconds': 0
            }
            
            if success and data:
                try:
                    start_time = time.time()
                    processed_record = self.processor.process(data)
                    processing_time = time.time() - start_time

                    if processed_record:
                        file_result.update({
                            'records_processed': 1,
                            'processing_time_seconds': round(processing_time, 3)
                        })

                        # Here you would customize your logic for handling the processed data
                        # For example: send to database, write to file, send to API, etc.
                        self._handle_processed_data(key, processed_record)

                        logger.info(f"Successfully processed {key}: 1 record in {processing_time:.3f}s")
                    else:
                        logger.warning(f"No valid data extracted from {key}")
                        file_result.update({
                            'records_processed': 0,
                            'processing_time_seconds': round(processing_time, 3)
                        })
                    
                except Exception as e:
                    error_msg = f"Error processing file {key}: {e}"
                    logger.error(error_msg)
                    file_result.update({
                        'success': False,
                        'error': str(e)
                    })
                    
                    if not self.config.continue_on_error:
                        raise
            else:
                logger.warning(f"Skipping file {key} due to download failure")
            
            batch_results.append(file_result)
        
        return batch_results
    
    def _handle_processed_data(self, file_key: str, processed_record: Dict[str, Any]):
        """
        Handle the processed data from a file (single record per file).

        CUSTOMIZE YOUR LOGIC HERE:
        - Send record to a database
        - Write to output file
        - Send to an API endpoint
        - Transform and forward to another system
        - Apply business logic and filtering
        - Generate alerts or notifications

        Example implementations:

        # Send to database
        # database.insert_ip_record(processed_record)

        # Send to API
        # response = requests.post('https://api.example.com/ip', json=processed_record)

        # Filter high-risk IPs
        # if processed_record.get('risk_score', 0) > 80:
        #     alert_system.send_alert(processed_record)
        """
        logger.info(f"Processing 1 record from {file_key}")

        # Example: Log high-risk IPs
        if processed_record.get('risk_score', 0) > 80:
            logger.warning(f"High-risk IP detected: {processed_record.get('ip_address')} "
                         f"(Risk: {processed_record.get('risk_score')}, "
                         f"Categories: {processed_record.get('detection_category', [])})")

        # TODO: Implement your specific data handling logic here
        pass
    
    def _update_stats(self, batch_results: List[Dict[str, Any]]):
        """Update processing statistics."""
        for result in batch_results:
            if result['success']:
                self.stats['total_files_processed'] += 1
                self.stats['total_records_processed'] += result['records_processed']
            else:
                self.stats['total_files_failed'] += 1
    
    def _log_final_stats(self):
        """Log final processing statistics."""
        duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
        
        logger.info("=" * 60)
        logger.info("BATCH PROCESSING COMPLETED")
        logger.info("=" * 60)
        logger.info(f"Total batches processed: {self.stats['total_batches_processed']}")
        logger.info(f"Total files processed: {self.stats['total_files_processed']}")
        logger.info(f"Total files failed: {self.stats['total_files_failed']}")
        logger.info(f"Total records processed: {self.stats['total_records_processed']}")
        logger.info(f"Total processing time: {duration:.2f} seconds")
        
        if self.stats['total_files_processed'] > 0:
            avg_records_per_file = self.stats['total_records_processed'] / self.stats['total_files_processed']
            avg_time_per_file = duration / self.stats['total_files_processed']
            logger.info(f"Average records per file: {avg_records_per_file:.1f}")
            logger.info(f"Average time per file: {avg_time_per_file:.3f} seconds")
        
        logger.info("=" * 60)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current processing statistics."""
        return self.stats.copy()
