"""
Main application module for Zombie IP S3 Ingest.
"""

import logging
from typing import Optional
from .config import Config
from .s3_client import S3Client
from .processor import IPProcessor

logger = logging.getLogger(__name__)


class ZombieIPIngestApp:
    """Main application class for processing zombie IP data from S3."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize the application with configuration."""
        self.config = config or Config()
        self.s3_client = S3Client(self.config)
        self.processor = IPProcessor(self.config)
        
    def run(self):
        """Run the main application logic."""
        logger.info("Starting zombie IP data ingestion from S3")
        
        try:
            # List objects in S3 bucket
            objects = self.s3_client.list_objects()
            logger.info(f"Found {len(objects)} objects to process")
            
            # Process each object
            for obj in objects:
                logger.info(f"Processing object: {obj}")
                data = self.s3_client.download_object(obj)
                processed_data = self.processor.process(data)
                logger.info(f"Processed {len(processed_data)} IP records")
                
        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise


def main():
    """Main entry point for the application."""
    app = ZombieIPIngestApp()
    app.run()
