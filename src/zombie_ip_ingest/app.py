"""
Main application module for Zombie IP S3 Ingest.
"""

import logging
from typing import Optional, Dict, Any
from .config import Config
from .s3_client import S3Client
from .processor import IPProcessor
from .batch_processor import BatchProcessor

logger = logging.getLogger(__name__)


class ZombieIPIngestApp:
    """Main application class for processing zombie IP data from S3."""

    def __init__(self, config: Optional[Config] = None):
        """Initialize the application with configuration."""
        self.config = config or Config()
        self.s3_client = S3Client(self.config)
        self.processor = IPProcessor(self.config)
        self.batch_processor = BatchProcessor(self.config, self.s3_client, self.processor)

    def run(self) -> Dict[str, Any]:
        """Run the main application logic with batch processing."""
        logger.info("Starting zombie IP data ingestion from S3 with batch processing")
        logger.info(f"Configuration: File batch size={self.config.file_batch_size}, "
                   f"Record batch size={self.config.batch_size}, "
                   f"Max workers={self.config.max_workers}, "
                   f"Parallel processing={'enabled' if self.config.enable_parallel_processing else 'disabled'}")

        try:
            # Use batch processor for efficient processing
            stats = self.batch_processor.process_all_files()

            logger.info("Zombie IP data ingestion completed successfully")
            return stats

        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise

    def run_single_file(self, file_key: str) -> Dict[str, Any]:
        """Process a single file for testing or specific use cases."""
        logger.info(f"Processing single file: {file_key}")

        try:
            # Download and process single file
            data = self.s3_client.download_object(file_key)
            processed_data = self.processor.process(data)

            result = {
                'file_key': file_key,
                'records_processed': len(processed_data),
                'success': True,
                'processed_data': processed_data
            }

            logger.info(f"Successfully processed {file_key}: {len(processed_data)} records")
            return result

        except Exception as e:
            logger.error(f"Error processing single file {file_key}: {e}")
            return {
                'file_key': file_key,
                'records_processed': 0,
                'success': False,
                'error': str(e)
            }


def main():
    """Main entry point for the application."""
    app = ZombieIPIngestApp()
    return app.run()
