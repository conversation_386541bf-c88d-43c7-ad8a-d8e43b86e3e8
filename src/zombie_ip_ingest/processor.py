"""
IP data processor module for handling zombie IP data.
"""

import json
import logging
from typing import List, Dict, Any, Union
import ipaddress
from .config import Config

logger = logging.getLogger(__name__)


class IPProcessor:
    """Processor for zombie IP data."""
    
    def __init__(self, config: Config):
        """Initialize processor with configuration."""
        self.config = config
        
    def process(self, data: bytes) -> List[Dict[str, Any]]:
        """Process raw data and extract IP information."""
        try:
            # Try to decode as text first
            text_data = data.decode('utf-8')

            # Try to parse as JSON
            try:
                json_data = json.loads(text_data)
                return self._process_json(json_data)
            except json.JSONDecodeError:
                # Try to parse as JSONL (JSON Lines) format
                try:
                    return self._process_jsonl(text_data)
                except Exception:
                    # If not JSON or JSONL, treat as text with IPs
                    return self._process_text(text_data)

        except UnicodeDecodeError:
            logger.error("Failed to decode data as UTF-8")
            raise
        except Exception as e:
            logger.error(f"Error processing data: {e}")
            raise

    def _process_jsonl(self, text: str) -> List[Dict[str, Any]]:
        """Process JSONL (JSON Lines) format data."""
        results = []
        lines = text.strip().split('\n')

        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line:
                try:
                    json_data = json.loads(line)
                    processed_item = self._extract_ip_info(json_data)
                    if processed_item:
                        results.append(processed_item)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON on line {line_num}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"Error processing line {line_num}: {e}")
                    if not self.config.continue_on_error:
                        raise

        logger.info(f"Processed JSONL data, extracted {len(results)} IP records from {len(lines)} lines")
        return results
    
    def _process_json(self, data: Union[Dict, List]) -> List[Dict[str, Any]]:
        """Process JSON data to extract IP information."""
        results = []
        
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    processed_item = self._extract_ip_info(item)
                    if processed_item:
                        results.append(processed_item)
        elif isinstance(data, dict):
            processed_item = self._extract_ip_info(data)
            if processed_item:
                results.append(processed_item)
        
        logger.info(f"Processed JSON data, extracted {len(results)} IP records")
        return results
    
    def _process_text(self, text: str) -> List[Dict[str, Any]]:
        """Process text data to extract IP addresses."""
        results = []
        lines = text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if line:
                ip_info = self._validate_and_process_ip(line)
                if ip_info:
                    results.append(ip_info)
        
        logger.info(f"Processed text data, extracted {len(results)} IP records")
        return results
    
    def _extract_ip_info(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Extract IP information from a dictionary item."""
        # Handle the specific structure from the sample data
        if 'payload' in item and isinstance(item['payload'], dict):
            payload = item['payload']

            # Look for identifier field which contains the IP
            if 'identifier' in payload:
                ip_value = payload['identifier']
                if isinstance(ip_value, str):
                    ip_info = self._validate_and_process_ip(ip_value)
                    if ip_info:
                        # Create enriched result with original data
                        result = {
                            'original_data': item,
                            'ip_address': ip_info['ip_address'],
                            'ip_version': ip_info['ip_version'],
                            'is_private': ip_info['is_private'],
                            'is_multicast': ip_info['is_multicast'],
                            'is_reserved': ip_info['is_reserved'],
                            'is_loopback': ip_info['is_loopback'],
                            'processed_timestamp': ip_info['processed_timestamp'],
                            # Extract additional fields from payload
                            'first_seen': payload.get('first_seen'),
                            'last_seen': payload.get('last_seen'),
                            'detection_category': payload.get('detection', {}).get('category', []),
                            'risk_score': payload.get('detection', {}).get('risk'),
                            'intensity': payload.get('detection', {}).get('intensity'),
                            'detection_methods': payload.get('detection_methods', []),
                            'country_code': payload.get('meta', {}).get('country_code'),
                            'port': payload.get('meta', {}).get('port'),
                            'protocol': payload.get('meta', {}).get('protocol'),
                            'object_type': payload.get('meta', {}).get('object_type'),
                            'action': payload.get('action'),
                            'type': payload.get('type'),
                            'timestamp': item.get('timestamp'),
                            'offset': item.get('offset')
                        }
                        return result

        # Fallback to original logic for other data structures
        ip_fields = ['ip', 'ip_address', 'address', 'host', 'source_ip', 'dest_ip']

        for field in ip_fields:
            if field in item:
                ip_value = item[field]
                if isinstance(ip_value, str):
                    ip_info = self._validate_and_process_ip(ip_value)
                    if ip_info:
                        # Merge with original data
                        result = item.copy()
                        result.update(ip_info)
                        return result

        return None
    
    def _validate_and_process_ip(self, ip_str: str) -> Dict[str, Any]:
        """Validate and process an IP address string."""
        try:
            # Clean the IP string
            ip_str = ip_str.strip()
            
            # Try to parse as IP address
            ip_obj = ipaddress.ip_address(ip_str)
            
            return {
                'ip_address': str(ip_obj),
                'ip_version': ip_obj.version,
                'is_private': ip_obj.is_private,
                'is_multicast': ip_obj.is_multicast,
                'is_reserved': ip_obj.is_reserved,
                'is_loopback': ip_obj.is_loopback,
                'processed_timestamp': self._get_current_timestamp()
            }
            
        except ValueError:
            logger.debug(f"Invalid IP address: {ip_str}")
            return None
        except Exception as e:
            logger.error(f"Error processing IP {ip_str}: {e}")
            return None
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.utcnow().isoformat() + 'Z'
