"""
IP data processor module for handling zombie IP data.
"""

import json
import logging
from typing import List, Dict, Any, Union
import ipaddress
from .config import Config

logger = logging.getLogger(__name__)


class IPProcessor:
    """Processor for zombie IP data."""
    
    def __init__(self, config: Config):
        """Initialize processor with configuration."""
        self.config = config
        
    def process(self, data: bytes) -> List[Dict[str, Any]]:
        """Process raw data and extract IP information."""
        try:
            # Try to decode as text first
            text_data = data.decode('utf-8')
            
            # Try to parse as JSON
            try:
                json_data = json.loads(text_data)
                return self._process_json(json_data)
            except json.JSONDecodeError:
                # If not JSON, treat as text with IPs
                return self._process_text(text_data)
                
        except UnicodeDecodeError:
            logger.error("Failed to decode data as UTF-8")
            raise
        except Exception as e:
            logger.error(f"Error processing data: {e}")
            raise
    
    def _process_json(self, data: Union[Dict, List]) -> List[Dict[str, Any]]:
        """Process JSON data to extract IP information."""
        results = []
        
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    processed_item = self._extract_ip_info(item)
                    if processed_item:
                        results.append(processed_item)
        elif isinstance(data, dict):
            processed_item = self._extract_ip_info(data)
            if processed_item:
                results.append(processed_item)
        
        logger.info(f"Processed JSON data, extracted {len(results)} IP records")
        return results
    
    def _process_text(self, text: str) -> List[Dict[str, Any]]:
        """Process text data to extract IP addresses."""
        results = []
        lines = text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if line:
                ip_info = self._validate_and_process_ip(line)
                if ip_info:
                    results.append(ip_info)
        
        logger.info(f"Processed text data, extracted {len(results)} IP records")
        return results
    
    def _extract_ip_info(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Extract IP information from a dictionary item."""
        # Look for common IP field names
        ip_fields = ['ip', 'ip_address', 'address', 'host', 'source_ip', 'dest_ip']
        
        for field in ip_fields:
            if field in item:
                ip_value = item[field]
                if isinstance(ip_value, str):
                    ip_info = self._validate_and_process_ip(ip_value)
                    if ip_info:
                        # Merge with original data
                        result = item.copy()
                        result.update(ip_info)
                        return result
        
        return None
    
    def _validate_and_process_ip(self, ip_str: str) -> Dict[str, Any]:
        """Validate and process an IP address string."""
        try:
            # Clean the IP string
            ip_str = ip_str.strip()
            
            # Try to parse as IP address
            ip_obj = ipaddress.ip_address(ip_str)
            
            return {
                'ip_address': str(ip_obj),
                'ip_version': ip_obj.version,
                'is_private': ip_obj.is_private,
                'is_multicast': ip_obj.is_multicast,
                'is_reserved': ip_obj.is_reserved,
                'is_loopback': ip_obj.is_loopback,
                'processed_timestamp': self._get_current_timestamp()
            }
            
        except ValueError:
            logger.debug(f"Invalid IP address: {ip_str}")
            return None
        except Exception as e:
            logger.error(f"Error processing IP {ip_str}: {e}")
            return None
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.utcnow().isoformat() + 'Z'
